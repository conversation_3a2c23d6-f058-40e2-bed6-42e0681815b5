using Identity.Application.Roles.Commands.CreateRole;
using Identity.Application.Roles.Commands.UpdateRole;
using Identity.Application.Roles.Commands.AssignPermissionsToRole;
using Identity.Application.Roles.Commands.RemovePermissionsFromRole;
using Identity.Application.Roles.Commands.ActivateRole;
using Identity.Application.Roles.Commands.DeactivateRole;
using Identity.Application.Roles.Queries.GetAllRoles;
using Identity.Application.Roles.Queries.GetRoleById;
using Identity.Application.Roles.Queries.GetRolePermissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Identity.API.Controllers
{
    // Option 1: Require authentication (Recommended)
    //[Authorize]
    [AllowAnonymous]
    public class RolesController : BaseController
    {
        /// <summary>
        /// Create a new role
        /// </summary>
        [AllowAnonymous]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreateRole([FromBody] CreateRoleRequest request)
        {
            //var currentUserId = GetCurrentUserId();

            var command = new CreateRoleCommand
            {
                Name = request.Name,
                Description = request.Description,
                Department = request.Department,
                Userid = request.Userid,
                Usertype = request.Usertype,
                ParentUserid = request.ParentUserid,
                ParentUserType = request.ParentUserType,
                //CreatedBy = currentUserId
            };

            var roleId = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetRole), new { id = roleId }, new { id = roleId });
        }

        /// <summary>
        /// Get all roles with filtering and pagination
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAllRoles(
            [FromQuery] string? searchTerm = null,
            [FromQuery] string? department = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] string? parentUserid = null,
            [FromQuery] int? parentUserType = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var query = new GetAllRolesQuery
                {
                    SearchTerm = searchTerm,
                    Department = department,
                    IsActive = isActive,
                    ParentUserid = parentUserid,
                    ParentUserType = parentUserType,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };

                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Get role by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetRole(Guid id)
        {
            try
            {
                var query = new GetRoleByIdQuery(id);
                var result = await Mediator.Send(query);

                if (result == null)
                {
                    return NotFound(new { message = $"Role with ID {id} not found" });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Update role
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateRole(Guid id, [FromBody] UpdateRoleRequest request)
        {
            try
            {
                // Get current user ID (commented out for anonymous access)
                // var currentUserId = GetCurrentUserId();

                var command = new UpdateRoleCommand
                {
                    Id = id,
                    Name = request.Name,
                    Description = request.Description,
                    Department = request.Department,
                    Userid = request.Userid,
                    Usertype = request.Usertype,
                    ParentUserid = request.ParentUserid,
                    ParentUserType = request.ParentUserType,
                    // UpdatedBy = currentUserId
                    UpdatedBy = Guid.Empty // Temporary for anonymous access
                };

                var result = await Mediator.Send(command);

                if (result)
                {
                    return Ok(new { message = "Role updated successfully", id = id });
                }

                return BadRequest(new { message = "Failed to update role" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Delete role
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DeleteRole(Guid id)
        {
            // This would be implemented with a DeleteRoleCommand
            return NoContent();
        }

        /// <summary>
        /// Get role permissions
        /// </summary>
        [HttpGet("{id}/permissions")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetRolePermissions(Guid id)
        {
            try
            {
                var query = new GetRolePermissionsQuery { RoleId = id };
                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Assign permissions to role
        /// </summary>
        [HttpPost("{id}/permissions")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> AssignPermissions(Guid id, [FromBody] AssignPermissionsRequest request)
        {
            try
            {
                var command = new AssignPermissionsToRoleCommand
                {
                    RoleId = id,
                    PermissionIds = request.PermissionIds
                };

                var result = await Mediator.Send(command);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Remove permissions from role
        /// </summary>
        [HttpDelete("{id}/permissions")]
        [AllowAnonymous] // For testing - remove in production
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> RemovePermissionsFromRole(Guid id, [FromBody] RemovePermissionsRequest request)
        {
            try
            {
                var command = new RemovePermissionsFromRoleCommand
                {
                    RoleId = id,
                    PermissionIds = request.PermissionIds
                };

                var result = await Mediator.Send(command);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Get role templates
        /// </summary>
        [HttpGet("templates")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetRoleTemplates()
        {
            // This would be implemented with a GetRoleTemplatesQuery
            return Ok(new { message = "GetRoleTemplates endpoint - to be implemented" });
        }

        /// <summary>
        /// Create role from template
        /// </summary>
        [HttpPost("from-template/{templateId}")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreateRoleFromTemplate(Guid templateId, [FromBody] CreateFromTemplateRequest request)
        {
            // This would be implemented with a CreateRoleFromTemplateCommand
            return Ok(new { message = "CreateRoleFromTemplate endpoint - to be implemented" });
        }

        // /// <summary>
        // /// Activate role
        // /// </summary>
        // [HttpPatch("{id}/activate")]
        // [AllowAnonymous]
        // [ProducesResponseType(StatusCodes.Status200OK)]
        // [ProducesResponseType(StatusCodes.Status404NotFound)]
        // [ProducesResponseType(StatusCodes.Status400BadRequest)]
        // [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        // [ProducesResponseType(StatusCodes.Status403Forbidden)]
        // public async Task<IActionResult> ActivateRole(Guid id, [FromBody] ActivateRoleRequest request)
        // {
        //     try
        //     {
        //         var command = new ActivateRoleCommand
        //         {
        //             RoleId = id,
        //             UpdatedBy = request.UpdatedBy ?? Guid.Empty // Temporary for anonymous access
        //         };

        //         var result = await Mediator.Send(command);

        //         if (result)
        //         {
        //             return Ok(new { message = "Role activated successfully", roleId = id, isActive = true });
        //         }

        //         return BadRequest(new { message = "Failed to activate role" });
        //     }
        //     catch (Exception ex)
        //     {
        //         return BadRequest(new { message = ex.Message });
        //     }
        // }

        // /// <summary>
        // /// Deactivate role
        // /// </summary>
        // [HttpPatch("{id}/deactivate")]
        // [AllowAnonymous]
        // [ProducesResponseType(StatusCodes.Status200OK)]
        // [ProducesResponseType(StatusCodes.Status404NotFound)]
        // [ProducesResponseType(StatusCodes.Status400BadRequest)]
        // [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        // [ProducesResponseType(StatusCodes.Status403Forbidden)]
        // public async Task<IActionResult> DeactivateRole(Guid id, [FromBody] DeactivateRoleRequest request)
        // {
        //     try
        //     {
        //         var command = new DeactivateRoleCommand
        //         {
        //             RoleId = id,
        //             UpdatedBy = request.UpdatedBy ?? Guid.Empty // Temporary for anonymous access
        //         };

        //         var result = await Mediator.Send(command);

        //         if (result)
        //         {
        //             return Ok(new { message = "Role deactivated successfully", roleId = id, isActive = false });
        //         }

        //         return BadRequest(new { message = "Failed to deactivate role" });
        //     }
        //     catch (Exception ex)
        //     {
        //         return BadRequest(new { message = ex.Message });
        //     }
        // }

        /// <summary>
        /// Update role active status (activate or deactivate based on isActive parameter)
        /// </summary>
        [HttpPatch("{id}/status")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateRoleStatus(Guid id, [FromBody] UpdateRoleStatusRequest request)
        {
            try
            {
                if (request.IsActive)
                {
                    var activateCommand = new ActivateRoleCommand
                    {
                        RoleId = id,
                        UpdatedBy = request.UpdatedBy ?? Guid.Empty // Temporary for anonymous access
                    };

                    var result = await Mediator.Send(activateCommand);
                    if (result)
                    {
                        return Ok(new { message = "Role activated successfully", roleId = id, isActive = true });
                    }
                    return BadRequest(new { message = "Failed to activate role" });
                }
                else
                {
                    var deactivateCommand = new DeactivateRoleCommand
                    {
                        RoleId = id,
                        UpdatedBy = request.UpdatedBy ?? Guid.Empty // Temporary for anonymous access
                    };

                    var result = await Mediator.Send(deactivateCommand);
                    if (result)
                    {
                        return Ok(new { message = "Role deactivated successfully", roleId = id, isActive = false });
                    }
                    return BadRequest(new { message = "Failed to deactivate role" });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }

    // Request DTOs
    public class CreateRoleRequest
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Department { get; set; }
        public string? Userid { get; set; }
        public int? Usertype { get; set; }
        public string? ParentUserid { get; set; }
        public int? ParentUserType { get; set; }
    }

    public class UpdateRoleRequest
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Department { get; set; }
        public string? Userid { get; set; }
        public int? Usertype { get; set; }
        public string? ParentUserid { get; set; }
        public int? ParentUserType { get; set; }
    }

    public class AssignPermissionsRequest
    {
        public System.Collections.Generic.List<Guid> PermissionIds { get; set; }
    }

    public class CreateFromTemplateRequest
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Department { get; set; }
    }

    public class RemovePermissionsRequest
    {
        public List<Guid> PermissionIds { get; set; } = new List<Guid>();
    }

    public class ActivateRoleRequest
    {
        public Guid? UpdatedBy { get; set; }
    }

    public class DeactivateRoleRequest
    {
        public Guid? UpdatedBy { get; set; }
    }

    public class UpdateRoleStatusRequest
    {
        public bool IsActive { get; set; }
        public Guid? UpdatedBy { get; set; }
    }
}
