using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Application.Common.Models;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Identity.Application.Auth.Commands.VerifyOtpLogin
{
    public class VerifyOtpLoginCommandHandler : IRequestHandler<VerifyOtpLoginCommand, OtpVerificationResponse>
    {
        private readonly IUserRepository _userRepository;
        private readonly IUserSessionRepository _sessionRepository;
        private readonly IOtpService _otpService;
        private readonly ITokenService _tokenService;
        private readonly ILogger<VerifyOtpLoginCommandHandler> _logger;

        public VerifyOtpLoginCommandHandler(
            IUserRepository userRepository,
            IUserSessionRepository sessionRepository,
            IOtpService otpService,
            ITokenService tokenService,
            ILogger<VerifyOtpLoginCommandHandler> logger)
        {
            _userRepository = userRepository;
            _sessionRepository = sessionRepository;
            _otpService = otpService;
            _tokenService = tokenService;
            _logger = logger;
        }

        public async Task<OtpVerificationResponse> Handle(VerifyOtpLoginCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing OTP verification for mobile number: {MobileNumber} and user type: {UserType}", MaskMobileNumber(request.MobileNumber), request.UserType);

                // Validate input parameters
                if (string.IsNullOrWhiteSpace(request.MobileNumber))
                {
                    _logger.LogWarning("Empty mobile number provided for OTP verification");
                    throw new ApplicationException("Mobile number is required");
                }

                if (string.IsNullOrWhiteSpace(request.OtpToken))
                {
                    _logger.LogWarning("Empty OTP token provided for mobile number: {MobileNumber}", MaskMobileNumber(request.MobileNumber));
                    throw new ApplicationException("OTP token is required");
                }

                // Format mobile number to ensure consistency (add +91 if not present)
                var formattedMobileNumber = FormatMobileNumber(request.MobileNumber);

                // Find user by mobile number and user type
                User? user;
                try
                {
                    user = await _userRepository.GetByPhoneNumberAndUserTypeAsync(formattedMobileNumber, request.UserType);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Database error while finding user with mobile number: {MobileNumber} and user type: {UserType}", MaskMobileNumber(formattedMobileNumber), request.UserType);
                    throw new ApplicationException("Unable to process request. Please try again later.");
                }

                if (user == null)
                {
                    _logger.LogWarning("User not found for OTP verification with mobile number: {MobileNumber} and user type: {UserType}", MaskMobileNumber(formattedMobileNumber), request.UserType);
                    throw new ApplicationException("Invalid credentials");
                }

                // Check if user is active
                if (user.IsActive == false)
                {
                    _logger.LogWarning("Inactive user attempted OTP verification: {UserId}", user.Id);
                    throw new ApplicationException("User is not active contact admin");
                }

                // Check if user is locked out
                if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTime.UtcNow)
                {
                    _logger.LogWarning("Locked out user attempted OTP verification: {UserId}", user.Id);
                    throw new ApplicationException("Account is temporarily locked");
                }

                // Validate OTP
                bool isValidOtp;
                try
                {
                    isValidOtp = await _otpService.ValidateOtpAsync(user.Id, request.OtpToken, OtpPurpose.Login);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error validating OTP for user {UserId}", user.Id);
                    throw new ApplicationException("Unable to validate OTP. Please try again.");
                }

                if (!isValidOtp)
                {
                    // Increment failed login attempts
                    user.IncrementAccessFailedCount();
                    await _userRepository.UpdateAsync(user);

                    // Check if account should be locked
                    if (user.AccessFailedCount >= 5 && user.LockoutEnabled)
                    {
                        user.LockoutUser(TimeSpan.FromMinutes(15));
                        await _userRepository.UpdateAsync(user);
                        _logger.LogWarning("User {UserId} account locked due to multiple failed OTP attempts", user.Id);
                        throw new ApplicationException("Account locked due to multiple failed attempts");
                    }

                    _logger.LogWarning("Invalid OTP provided for user: {UserId}", user.Id);
                    throw new ApplicationException("Invalid OTP");
                }

                // Reset failed login attempts and update user
                user.ResetAccessFailedCount();
                await _userRepository.UpdateAsync(user);

                // Detach the user entity to avoid tracking conflicts
                await _userRepository.DetachAsync(user);
                var tokenResponse = await _tokenService.GenerateTokensAsync(user.Id);
                // Generate tokens
                // TokenResponse tokenResponse;
                // try
                // {
                //     tokenResponse = await _tokenService.GenerateTokensAsync(user.Id);
                //     if (tokenResponse == null)
                //     {
                //         _logger.LogError("Token generation returned null for user {UserId}", user.Id);
                //         throw new ApplicationException("Unable to generate authentication tokens");
                //     }
                // }
                // catch (Exception ex) when (!(ex is ApplicationException))
                // {
                //     _logger.LogError(ex, "Error generating tokens for user {UserId}", user.Id);
                //     throw new ApplicationException("Unable to generate authentication tokens. Please try again.");
                // }

                // Get all users with the same mobile number to find other user types
                var allUsersWithSameMobile = await _userRepository.GetAllByPhoneNumberAsync(formattedMobileNumber);
                var otherUserTypes = allUsersWithSameMobile
                    .Select(u => u.UserType)
                    .Distinct()
                    .ToList();
                // Create user session
                var session = new UserSession(
                    user.Id,
                    tokenResponse.AccessToken,
                    request.DeviceInfo,
                    request.IpAddress,
                    tokenResponse.ExpiresAt);

                await _sessionRepository.AddAsync(session);

                // Invalidate any remaining OTP tokens for this user
                await _otpService.InvalidateUserOtpsAsync(user.Id, OtpPurpose.Login);

                _logger.LogInformation("User {UserId} logged in successfully via OTP", user.Id);

                // Return enhanced response with user details and other user types
                return new OtpVerificationResponse
                {
                    AccessToken = tokenResponse.AccessToken,
                    RefreshToken = tokenResponse.RefreshToken,
                    ExpiresAt = tokenResponse.ExpiresAt,
                    UserId = tokenResponse.UserId,
                    Username = tokenResponse.Username,
                    FirstName = string.IsNullOrWhiteSpace(user.FirstName)
    ? allUsersWithSameMobile.FirstOrDefault(u => !string.IsNullOrWhiteSpace(u.FirstName))?.FirstName ?? string.Empty
    : user.FirstName,
                    LastName = string.IsNullOrWhiteSpace(user.LastName)
    ? allUsersWithSameMobile.FirstOrDefault(u => !string.IsNullOrWhiteSpace(u.LastName))?.LastName ?? string.Empty
    : user.LastName,
                    Email = user.Email?.Value ?? allUsersWithSameMobile.FirstOrDefault(u => u.Email != null)?.Email?.Value ?? string.Empty,
                    MobileNumber = user.PhoneNumber ?? string.Empty,
                    //                 Password = string.IsNullOrWhiteSpace(user.PasswordHash)
                    // ? allUsersWithSameMobile.FirstOrDefault(u => !string.IsNullOrWhiteSpace(u.PasswordHash))?.PasswordHash
                    // : user.PasswordHash ?? string.Empty,
                    //                 ConfirmPassword = string.IsNullOrWhiteSpace(user.PasswordHash)
                    // ? allUsersWithSameMobile.FirstOrDefault(u => !string.IsNullOrWhiteSpace(u.PasswordHash))?.PasswordHash
                    // : user.PasswordHash ?? string.Empty,
                    UserType = user.UserType,
                    OtherUserTypes = otherUserTypes,
                };
            }
            catch (ApplicationException)
            {
                // Re-throw application exceptions as they contain user-friendly messages
                throw;
            }
            catch (ArgumentException argEx)
            {
                _logger.LogError(argEx, "Invalid argument in OTP verification for mobile number: {MobileNumber} and user type: {UserType}", MaskMobileNumber(request.MobileNumber), request.UserType);
                throw new ApplicationException("Invalid request parameters");
            }
            catch (InvalidOperationException invOpEx)
            {
                _logger.LogError(invOpEx, "Invalid operation in OTP verification for mobile number: {MobileNumber} and user type: {UserType}", MaskMobileNumber(request.MobileNumber), request.UserType);
                throw new ApplicationException("Operation not allowed at this time");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error processing OTP verification for mobile number: {MobileNumber} and user type: {UserType}. Error: {ErrorMessage}", MaskMobileNumber(request.MobileNumber), request.UserType, ex.Message);
                throw new ApplicationException("An error occurred while processing your request. Please try again later.");
            }
        }

        private string FormatMobileNumber(string mobileNumber)
        {
            if (string.IsNullOrWhiteSpace(mobileNumber))
                return mobileNumber;

            // Remove any spaces, dashes, or special characters
            var cleanNumber = mobileNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

            // Add +91 if not present and it's a 10-digit number starting with 6-9
            if (!cleanNumber.StartsWith("+91") && cleanNumber.Length == 10 && cleanNumber[0] >= '6' && cleanNumber[0] <= '9')
            {
                cleanNumber = "+91" + cleanNumber;
            }

            return cleanNumber;
        }

        private string MaskMobileNumber(string mobileNumber)
        {
            if (string.IsNullOrWhiteSpace(mobileNumber) || mobileNumber.Length < 4)
                return "****";

            // Handle +91 prefix
            if (mobileNumber.StartsWith("+91"))
            {
                var numberPart = mobileNumber.Substring(3);
                if (numberPart.Length >= 6)
                {
                    return "+91" + numberPart.Substring(0, 2) + "****" + numberPart.Substring(numberPart.Length - 2);
                }
            }

            // Fallback for other formats
            var visiblePart = mobileNumber.Length > 6 ? 3 : 2;
            var maskedPart = new string('*', mobileNumber.Length - (visiblePart * 2));
            return mobileNumber.Substring(0, visiblePart) + maskedPart + mobileNumber.Substring(mobileNumber.Length - visiblePart);
        }
    }
}
