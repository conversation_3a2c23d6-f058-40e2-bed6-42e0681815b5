using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Exceptions;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;

namespace Identity.Application.Roles.Commands.ActivateRole
{
    public class ActivateRoleCommandHandler : IRequestHandler<ActivateRoleCommand, bool>
    {
        private readonly Identity.Domain.Repositories.IRoleRepository _roleRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<ActivateRoleCommandHandler> _logger;

        public ActivateRoleCommandHandler(
            Identity.Domain.Repositories.IRoleRepository roleRepository,
            IMessageBroker messageBroker,
            ILogger<ActivateRoleCommandHandler> logger)
        {
            _roleRepository = roleRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<bool> Handle(ActivateRoleCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Activating role with ID: {RoleId}", request.RoleId);

                var role = await _roleRepository.GetByIdAsync(request.RoleId);
                if (role == null)
                {
                    throw new DomainException($"Role with ID {request.RoleId} not found");
                }

                // Check if role is already active
                if (role.IsActive)
                {
                    _logger.LogInformation("Role {RoleId} is already active", request.RoleId);
                    return true;
                }

                // Activate the role
                role.Activate();
                await _roleRepository.UpdateAsync(role);

                // Publish integration event
                await _messageBroker.PublishAsync("role.activated", new
                {
                    RoleId = role.Id,
                    Name = role.Name,
                    IsActive = true,
                    ActivatedBy = request.UpdatedBy,
                    ActivatedAt = DateTime.UtcNow
                });

                _logger.LogInformation("Role activated successfully: {RoleId} - {RoleName}", role.Id, role.Name);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating role with ID {RoleId}", request.RoleId);
                throw;
            }
        }
    }
}
