using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Exceptions;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;

namespace Identity.Application.Roles.Commands.DeactivateRole
{
    public class DeactivateRoleCommandHandler : IRequestHandler<DeactivateRoleCommand, bool>
    {
        private readonly Identity.Domain.Repositories.IRoleRepository _roleRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<DeactivateRoleCommandHandler> _logger;

        public DeactivateRoleCommandHandler(
            Identity.Domain.Repositories.IRoleRepository roleRepository,
            IMessageBroker messageBroker,
            ILogger<DeactivateRoleCommandHandler> logger)
        {
            _roleRepository = roleRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<bool> Handle(DeactivateRoleCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Deactivating role with ID: {RoleId}", request.RoleId);

                var role = await _roleRepository.GetByIdAsync(request.RoleId);
                if (role == null)
                {
                    throw new DomainException($"Role with ID {request.RoleId} not found");
                }

                // Check if role is already inactive
                if (!role.IsActive)
                {
                    _logger.LogInformation("Role {RoleId} is already inactive", request.RoleId);
                    return true;
                }

                // Deactivate the role (this will throw DomainException if it's a system role)
                role.Deactivate();
                await _roleRepository.UpdateAsync(role);

                // Publish integration event
                await _messageBroker.PublishAsync("role.deactivated", new
                {
                    RoleId = role.Id,
                    Name = role.Name,
                    IsActive = false,
                    DeactivatedBy = request.UpdatedBy,
                    DeactivatedAt = DateTime.UtcNow
                });

                _logger.LogInformation("Role deactivated successfully: {RoleId} - {RoleName}", role.Id, role.Name);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating role with ID {RoleId}", request.RoleId);
                throw;
            }
        }
    }
}
