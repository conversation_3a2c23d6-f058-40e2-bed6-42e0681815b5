using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Application.Common.Models;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Identity.Application.Auth.Commands.MobilePasswordLogin
{
    public class MobilePasswordLoginCommandHandler : IRequestHandler<MobilePasswordLoginCommand, OtpVerificationResponse>
    {
        private readonly IUserRepository _userRepository;
        private readonly IUserSessionRepository _sessionRepository;
        private readonly IPasswordHasher _passwordHasher;
        private readonly ITokenService _tokenService;
        private readonly IUserRolePermissionService _userRolePermissionService;
        private readonly ILogger<MobilePasswordLoginCommandHandler> _logger;

        public MobilePasswordLoginCommandHandler(
            IUserRepository userRepository,
            IUserSessionRepository sessionRepository,
            IPasswordHasher passwordHasher,
            ITokenService tokenService,
            IUserRolePermissionService userRolePermissionService,
            ILogger<MobilePasswordLoginCommandHandler> logger)
        {
            _userRepository = userRepository;
            _sessionRepository = sessionRepository;
            _passwordHasher = passwordHasher;
            _tokenService = tokenService;
            _userRolePermissionService = userRolePermissionService;
            _logger = logger;
        }

        public async Task<OtpVerificationResponse> Handle(MobilePasswordLoginCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing password login. IsAdmin: {IsAdmin}, UserType: {UserType}",
                    request.IsAdmin, request.UserType);

                Domain.Entities.User user;

                if (request.IsAdmin)
                {
                    // Admin flow: use email and usertype
                    if (string.IsNullOrWhiteSpace(request.Email))
                    {
                        throw new ApplicationException("Email is required for admin login");
                    }

                    _logger.LogInformation("Admin login for email: {Email} and user type: {UserType}",
                        MaskEmail(request.Email), request.UserType);

                    user = await _userRepository.GetByEmailAndUserTypeAsync(request.Email, request.UserType);
                    if (user == null)
                    {
                        _logger.LogWarning("User not found for admin login with email: {Email} and user type: {UserType}",
                            MaskEmail(request.Email), request.UserType);
                        throw new ApplicationException("Invalid credentials");
                    }

                    // Check if user is active
                    if (user.IsActive == false)
                    {
                        _logger.LogWarning("Inactive user attempted admin login: {UserId}", user.Id);
                        throw new ApplicationException("User is not active contact admin");
                    }
                }
                else
                {
                    // User flow: use mobile number and usertype
                    if (string.IsNullOrWhiteSpace(request.MobileNumber))
                    {
                        throw new ApplicationException("Mobile number is required for user login");
                    }

                    // Format mobile number to ensure consistency (add +91 if not present)
                    var formattedMobileNumber = FormatMobileNumber(request.MobileNumber);

                    _logger.LogInformation("User login for mobile number: {MobileNumber} and user type: {UserType}",
                        MaskMobileNumber(formattedMobileNumber), request.UserType);

                    user = await _userRepository.GetByPhoneNumberAndUserTypeAsync(formattedMobileNumber, request.UserType);
                    if (user == null)
                    {
                        _logger.LogWarning("User not found for user login with mobile number: {MobileNumber} and user type: {UserType}",
                            MaskMobileNumber(formattedMobileNumber), request.UserType);
                        throw new ApplicationException("Invalid credentials");
                    }

                    // Check if user is active
                    if (user.IsActive == false)
                    {
                        _logger.LogWarning("Inactive user attempted mobile login: {UserId}", user.Id);
                        throw new ApplicationException("User is not active contact admin");
                    }
                }

                // Check if user has a password set
                if (string.IsNullOrWhiteSpace(user.PasswordHash))
                {
                    _logger.LogWarning("User {UserId} attempted login but no password is set", user.Id);
                    throw new ApplicationException("Password not set for this account");
                }

                // Check if user is locked out
                if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTime.UtcNow)
                {
                    _logger.LogWarning("Locked out user attempted mobile password login: {UserId}", user.Id);
                    throw new ApplicationException("Account is temporarily locked");
                }

                // Verify the password
                if (!_passwordHasher.VerifyPassword(user.PasswordHash, request.Password))
                {
                    // Increment failed login attempts
                    user.IncrementAccessFailedCount();
                    await _userRepository.UpdateAsync(user);

                    // Check if account should be locked
                    if (user.AccessFailedCount >= 5 && user.LockoutEnabled)
                    {
                        user.LockoutUser(TimeSpan.FromMinutes(15));
                        await _userRepository.UpdateAsync(user);
                        _logger.LogWarning("User {UserId} account locked due to multiple failed password attempts", user.Id);
                        throw new ApplicationException("Account locked due to multiple failed attempts");
                    }

                    _logger.LogWarning("Invalid password provided for user: {UserId}", user.Id);
                    throw new ApplicationException("Invalid credentials");
                }

                // Check if account is suspended
                if (user.Status == UserStatus.Suspended)
                {
                    _logger.LogWarning("Suspended user attempted mobile password login: {UserId}", user.Id);
                    throw new ApplicationException("Account is suspended. Please contact support");
                }

                // Reset failed login attempts and update user
                user.ResetAccessFailedCount();
                await _userRepository.UpdateAsync(user);

                // Detach the user entity to avoid tracking conflicts
                await _userRepository.DetachAsync(user);

                // Generate tokens
                var tokenResponse = await _tokenService.GenerateTokensAsync(user.Id);

                // Get all users with the same identifier to find other user types
                IEnumerable<Domain.Entities.User> allUsersWithSameIdentifier;
                if (request.IsAdmin)
                {
                    // For admin login, get all users with the same email
                    allUsersWithSameIdentifier = await _userRepository.GetAllByEmailAsync(request.Email!);
                }
                else
                {
                    // For user login, get all users with the same mobile number
                    var formattedMobileNumber = FormatMobileNumber(request.MobileNumber!);
                    allUsersWithSameIdentifier = await _userRepository.GetAllByPhoneNumberAsync(formattedMobileNumber);
                }

                var otherUserTypes = allUsersWithSameIdentifier
                    .Select(u => u.UserType)
                    .Distinct()
                    .ToList();

                // Create user session
                var session = new UserSession(
                    user.Id,
                    tokenResponse.AccessToken,
                    request.DeviceInfo,
                    request.IpAddress,
                    tokenResponse.ExpiresAt);

                await _sessionRepository.AddAsync(session);

                _logger.LogInformation("User {UserId} logged in successfully via mobile password", user.Id);

                // Get roles and permissions for all user types
                var roles = new List<RoleDto>();
                _logger.LogInformation("Fetching roles and permissions for user type {UserType}", user.UserType);
                try
                {
                    roles = await _userRolePermissionService.GetUserRolesWithPermissionsAsync(user.Id);
                    _logger.LogInformation("Successfully fetched {RoleCount} roles for user {UserId}", roles.Count, user.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error fetching roles and permissions for user {UserId}", user.Id);
                    // Continue with empty roles list rather than failing the login
                }

                // Return enhanced response with user details, other user types, and roles
                return new OtpVerificationResponse
                {
                    AccessToken = tokenResponse.AccessToken,
                    RefreshToken = tokenResponse.RefreshToken,
                    ExpiresAt = tokenResponse.ExpiresAt,
                    UserId = tokenResponse.UserId,
                    Username = tokenResponse.Username,
                    FirstName = string.IsNullOrWhiteSpace(user.FirstName)
                        ? allUsersWithSameIdentifier.FirstOrDefault(u => !string.IsNullOrWhiteSpace(u.FirstName))?.FirstName
                        : user.FirstName,
                    LastName = string.IsNullOrWhiteSpace(user.LastName)
                        ? allUsersWithSameIdentifier.FirstOrDefault(u => !string.IsNullOrWhiteSpace(u.LastName))?.LastName
                        : user.LastName,
                    Email = user.Email?.Value ?? allUsersWithSameIdentifier.FirstOrDefault(u => u.Email != null)?.Email?.Value,
                    MobileNumber = request.IsAdmin
                        ? (user.PhoneNumber ?? allUsersWithSameIdentifier.FirstOrDefault(u => !string.IsNullOrWhiteSpace(u.PhoneNumber))?.PhoneNumber)
                        : user.PhoneNumber,
                    Password = user.PasswordHash ?? string.Empty,
                    ConfirmPassword = user.PasswordHash ?? string.Empty,
                    UserType = user.UserType,
                    OtherUserTypes = otherUserTypes,
                    Roles = roles
                };
            }
            catch (ApplicationException)
            {
                throw; // Re-throw application exceptions as-is
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing mobile password login for mobile number: {MobileNumber} and user type: {UserType}",
                    MaskMobileNumber(request.MobileNumber), request.UserType);
                throw new ApplicationException("An error occurred while processing your request");
            }
        }

        private static string FormatMobileNumber(string mobileNumber)
        {
            if (string.IsNullOrWhiteSpace(mobileNumber))
                return mobileNumber;

            // Remove any spaces, dashes, or special characters
            var cleanNumber = mobileNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

            // Add +91 if not present and it's a 10-digit number starting with 6-9
            if (!cleanNumber.StartsWith("+91") && cleanNumber.Length == 10 && cleanNumber[0] >= '6' && cleanNumber[0] <= '9')
            {
                cleanNumber = "+91" + cleanNumber;
            }

            return cleanNumber;
        }

        private static string MaskMobileNumber(string mobileNumber)
        {
            if (string.IsNullOrWhiteSpace(mobileNumber) || mobileNumber.Length < 4)
                return "****";

            return mobileNumber.Substring(0, Math.Min(4, mobileNumber.Length)) + "****" +
                   mobileNumber.Substring(Math.Max(4, mobileNumber.Length - 2));
        }

        private static string MaskEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email) || !email.Contains("@"))
                return "****";

            var parts = email.Split('@');
            var localPart = parts[0];
            var domainPart = parts[1];

            if (localPart.Length <= 2)
                return "**@" + domainPart;

            return localPart.Substring(0, 2) + "****@" + domainPart;
        }


    }
}
